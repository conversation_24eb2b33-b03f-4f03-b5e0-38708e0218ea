#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通道布局修复测试脚本
修复计数和用时空间分配问题，以及Rs/Rct对齐问题

Author: Assistant
Date: 2025-08-14
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QFrame, QGroupBox, QLineEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ChannelLayoutTestWindow(QMainWindow):
    """通道布局测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("通道布局修复测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建主界面
        self._init_ui()
    
    def _init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("通道布局修复对比测试")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 创建对比区域
        comparison_layout = QHBoxLayout()
        comparison_layout.setSpacing(30)
        
        # 修复前的布局
        before_widget = self._create_demo_channel("修复前布局", "before")
        comparison_layout.addWidget(before_widget)
        
        # 修复后的布局
        after_widget = self._create_demo_channel("修复后布局", "after")
        comparison_layout.addWidget(after_widget)
        
        main_layout.addLayout(comparison_layout)
        
        # 说明文字
        info_label = QLabel("""
        修复内容：
        1. 计数和用时空间均分：给计数分配更多显示空间（权重3:2）
        2. Rs和Rct对齐修复：标题和数值框居中对齐，添加边框使显示更清晰
        3. 整体布局优化：改善视觉效果和用户体验
        """)
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #3498db;
            }
        """)
        main_layout.addWidget(info_label)
    
    def _create_demo_channel(self, title, layout_type):
        """创建演示通道"""
        demo_widget = QWidget()
        demo_layout = QVBoxLayout(demo_widget)
        demo_layout.setContentsMargins(15, 15, 15, 15)
        demo_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                background-color: {'#e74c3c' if layout_type == 'before' else '#27ae60'};
                padding: 10px;
                border-radius: 6px;
                font-size: 12pt;
            }}
        """)
        demo_layout.addWidget(title_label)
        
        # 通道模拟框
        channel_frame = QGroupBox("通道 3")
        channel_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 15px;
                background-color: #ffffff;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                font-size: 11pt;
            }
        """)
        
        channel_layout = QVBoxLayout(channel_frame)
        channel_layout.setContentsMargins(10, 10, 10, 10)
        channel_layout.setSpacing(8)
        
        # 主内容区域
        main_content = QFrame()
        main_content_layout = QHBoxLayout(main_content)
        main_content_layout.setSpacing(15)
        main_content_layout.setContentsMargins(5, 5, 5, 5)
        
        # 左列
        left_column = self._create_left_column_demo(layout_type)
        main_content_layout.addLayout(left_column, 2)
        
        # 右列
        right_column = self._create_right_column_demo(layout_type)
        main_content_layout.addLayout(right_column, 3)
        
        channel_layout.addWidget(main_content)
        demo_layout.addWidget(channel_frame)
        
        return demo_widget
    
    def _create_left_column_demo(self, layout_type):
        """创建左列演示"""
        left_layout = QVBoxLayout()
        left_layout.setSpacing(6)
        
        # 计数和时间区域
        if layout_type == "before":
            # 修复前的布局
            count_time_layout = self._create_old_count_time_layout()
        else:
            # 修复后的布局
            count_time_layout = self._create_new_count_time_layout()
        
        left_layout.addLayout(count_time_layout)
        
        # 电池码输入
        battery_layout = QHBoxLayout()
        battery_layout.setSpacing(6)
        
        battery_label = QLabel("电池:")
        battery_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        battery_layout.addWidget(battery_label)
        
        battery_edit = QLineEdit()
        battery_edit.setPlaceholderText("扫码或输入电池条码")
        battery_edit.setStyleSheet("""
            QLineEdit {
                font-size: 9pt;
                padding: 4px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ffffff;
            }
        """)
        battery_layout.addWidget(battery_edit)
        
        left_layout.addLayout(battery_layout)
        
        # 电压显示
        voltage_layout = QHBoxLayout()
        voltage_layout.setSpacing(6)
        
        voltage_label = QLabel("电压:")
        voltage_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        voltage_layout.addWidget(voltage_label)
        
        voltage_value = QLabel("0.000")
        voltage_value.setStyleSheet("font-size: 9pt; color: #2c3e50; font-weight: bold;")
        voltage_layout.addWidget(voltage_value)
        
        voltage_layout.addStretch()
        left_layout.addLayout(voltage_layout)
        
        left_layout.addStretch()
        return left_layout
    
    def _create_old_count_time_layout(self):
        """创建修复前的计数时间布局"""
        count_time_layout = QHBoxLayout()
        count_time_layout.setSpacing(8)
        
        # 计数 - 空间太小
        count_label = QLabel("计数:")
        count_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        count_label.setMinimumWidth(15)
        count_label.setMaximumWidth(20)
        count_time_layout.addWidget(count_label)
        
        count_value = QLabel("10")
        count_value.setStyleSheet("font-size: 10pt; color: #27ae60; font-weight: bold;")
        count_value.setMinimumWidth(15)
        count_value.setMaximumWidth(25)
        count_time_layout.addWidget(count_value)
        
        separator = QLabel("|")
        separator.setStyleSheet("font-size: 10pt; color: #bdc3c7;")
        separator.setMaximumWidth(10)
        count_time_layout.addWidget(separator)
        
        # 用时
        time_label = QLabel("用时:")
        time_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        time_label.setMinimumWidth(15)
        time_label.setMaximumWidth(20)
        count_time_layout.addWidget(time_label)
        
        time_value = QLabel("00:00:00")
        time_value.setStyleSheet("font-size: 10pt; color: #2c3e50; font-weight: bold;")
        time_value.setMinimumWidth(30)
        time_value.setMaximumWidth(40)
        count_time_layout.addWidget(time_value)
        
        count_time_layout.addStretch()
        return count_time_layout
    
    def _create_new_count_time_layout(self):
        """创建修复后的计数时间布局"""
        count_time_layout = QHBoxLayout()
        count_time_layout.setSpacing(6)
        
        # 计数区域容器 - 更多空间
        count_container = QHBoxLayout()
        count_container.setSpacing(4)
        
        count_label = QLabel("计数:")
        count_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        count_label.setMinimumWidth(25)
        count_container.addWidget(count_label)
        
        count_value = QLabel("10")
        count_value.setStyleSheet("font-size: 11pt; color: #27ae60; font-weight: bold;")
        count_value.setMinimumWidth(35)
        count_value.setMaximumWidth(50)
        count_container.addWidget(count_value)
        
        count_time_layout.addLayout(count_container, 3)  # 权重3
        
        separator = QLabel("|")
        separator.setStyleSheet("font-size: 10pt; color: #bdc3c7;")
        separator.setMaximumWidth(8)
        count_time_layout.addWidget(separator)
        
        # 用时区域容器
        time_container = QHBoxLayout()
        time_container.setSpacing(4)
        
        time_label = QLabel("用时:")
        time_label.setStyleSheet("font-size: 9pt; color: #7f8c8d; font-weight: bold;")
        time_label.setMinimumWidth(25)
        time_container.addWidget(time_label)
        
        time_value = QLabel("00:00:00")
        time_value.setStyleSheet("font-size: 10pt; color: #2c3e50; font-weight: bold;")
        time_value.setMinimumWidth(55)
        time_value.setMaximumWidth(65)
        time_container.addWidget(time_value)
        
        count_time_layout.addLayout(time_container, 2)  # 权重2
        count_time_layout.addStretch(1)
        
        return count_time_layout
    
    def _create_right_column_demo(self, layout_type):
        """创建右列演示"""
        right_layout = QVBoxLayout()
        right_layout.setSpacing(6)
        
        if layout_type == "before":
            # 修复前的Rs/Rct布局
            self._create_old_impedance_display(right_layout, "Rs(mΩ)", "0.000")
            self._create_old_impedance_display(right_layout, "Rct(mΩ)", "0.000")
        else:
            # 修复后的Rs/Rct布局
            self._create_new_impedance_display(right_layout, "Rs(mΩ)", "0.000")
            self._create_new_impedance_display(right_layout, "Rct(mΩ)", "0.000")
        
        right_layout.addStretch()
        return right_layout
    
    def _create_old_impedance_display(self, layout, title, value):
        """创建修复前的阻抗显示"""
        impedance_layout = QHBoxLayout()
        impedance_layout.setSpacing(6)
        
        impedance_layout.addStretch(5)  # 大量左侧空间
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 10pt; color: #7f8c8d; font-weight: bold;")
        title_label.setMinimumWidth(80)
        title_label.setMaximumWidth(85)
        title_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        impedance_layout.addWidget(title_label)
        
        # 数值框太小，不对齐
        value_label = QLabel(value)
        value_label.setMinimumWidth(9)
        value_label.setMaximumWidth(9)
        value_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        value_label.setStyleSheet("font-size: 11pt; font-weight: bold; color: #2c3e50;")
        impedance_layout.addWidget(value_label)
        
        layout.addLayout(impedance_layout)
    
    def _create_new_impedance_display(self, layout, title, value):
        """创建修复后的阻抗显示"""
        impedance_layout = QHBoxLayout()
        impedance_layout.setSpacing(8)
        
        content_container = QHBoxLayout()
        content_container.setSpacing(6)
        
        # 标题固定宽度
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 10pt; color: #7f8c8d; font-weight: bold;")
        title_label.setMinimumWidth(65)
        title_label.setMaximumWidth(65)
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        content_container.addWidget(title_label)
        
        # 数值框对齐，有边框
        value_label = QLabel(value)
        value_label.setMinimumWidth(60)
        value_label.setMaximumWidth(80)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet("""
            QLabel {
                font-size: 11pt; 
                font-weight: bold; 
                color: #2c3e50;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 4px 8px;
                text-align: center;
            }
        """)
        content_container.addWidget(value_label)
        
        impedance_layout.addStretch(1)
        impedance_layout.addLayout(content_container)
        impedance_layout.addStretch(0)
        
        layout.addLayout(impedance_layout)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = ChannelLayoutTestWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
