Metadata-Version: 2.4
Name: svglib
Version: 1.5.1
Summary: A pure-Python library for reading and converting SVG
Home-page: https://github.com/deeplook/svglib
Author: <PERSON><PERSON>
Author-email: <EMAIL>
License: LGPL 3
Keywords: SVG,PDF,reportlab,conversion,graphics
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Natural Language :: English
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Documentation
Classifier: Topic :: Utilities
Classifier: Topic :: Printing
Classifier: Topic :: Multimedia :: Graphics :: Graphics Conversion
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Text Processing :: Markup :: XML
Requires-Python: >=3.7
License-File: LICENSE.txt
Requires-Dist: reportlab
Requires-Dist: lxml
Requires-Dist: tinycss2>=0.6.0
Requires-Dist: cssselect2>=0.2.0
Dynamic: license-file
Dynamic: requires-dist

.. -*- mode: rst -*-

======
Svglib
======

---------------------------------------------------------------------------
A pure-Python library for reading and converting SVG
---------------------------------------------------------------------------

.. image:: https://github.com/deeplook/svglib/workflows/CI/badge.svg
  :target: https://github.com/deeplook/svglib/actions?query=workflow%3ACI

.. image:: https://results.pre-commit.ci/badge/github/deeplook/svglib/master.svg
  :target: https://results.pre-commit.ci/latest/github/deeplook/svglib/master
  :alt: pre-commit.ci status

.. image:: https://pyup.io/repos/github/deeplook/svglib/shield.svg
  :target: https://pyup.io/repos/github/deeplook/svglib/

.. image:: https://img.shields.io/pypi/implementation/svglib.svg
  :target: https://pypi.org/project/svglib

.. image:: https://img.shields.io/pypi/pyversions/svglib.svg
  :target: https://pypi.org/project/svglib

.. image:: https://img.shields.io/pypi/dm/svglib.svg
  :target: https://pepy.tech/project/svglib

.. image:: https://img.shields.io/pypi/v/svglib.svg
  :target: https://pypi.org/project/svglib

.. image:: https://img.shields.io/conda/vn/conda-forge/svglib.svg
  :target: https://github.com/conda-forge/svglib-feedstock

.. image:: https://img.shields.io/conda/dn/conda-forge/svglib.svg
  :target: https://github.com/conda-forge/svglib-feedstock

.. image:: https://img.shields.io/conda/pn/conda-forge/svglib.svg
  :target: https://pypi.org/project/svglib

.. image:: https://img.shields.io/pypi/l/svglib.svg
  :target: https://pypi.org/project/svglib

.. image:: https://static.streamlit.io/badges/streamlit_badge_black_white.svg
  :target: https://share.streamlit.io/deeplook/streamlit-svglib/master/streamlit_app.py


About
-----

``Svglib`` is a pure-Python library for reading SVG_ files and converting
them (to a reasonable degree) to other formats using the ReportLab_ Open
Source toolkit.

Used as a package you can read existing SVG files and convert them into
ReportLab ``Drawing`` objects that can be used in a variety of contexts,
e.g. as ReportLab Platypus ``Flowable`` objects or in RML_.
As a command-line tool it converts SVG files into PDF ones (but adding
other output formats like bitmap or EPS is really easy and will be better
supported, soon).

Tests include a huge `W3C SVG test suite`_ plus ca. 200 `flags from
Wikipedia`_ and some selected `symbols from Wikipedia`_ (with increasingly
less pointing to missing features).


Features
--------

- convert SVG_ files into ReportLab_ Graphics ``Drawing`` objects
- handle plain or compressed SVG files (.svg and .svgz)
- allow patterns for output files on command-line
- install a Python package named ``svglib``
- install a Python command-line script named ``svg2pdf``
- provide a PyTest_ test suite with over 90% code coverage
- test entire `W3C SVG test suite`_ after pulling from the internet
- test all SVG `flags from Wikipedia`_ after pulling from the internet
- test selected SVG `symbols from Wikipedia`_ after pulling from the net
- support Python 3.7+ and PyPy3


Known limitations
-----------------

- @import rules in stylesheets are ignored. CSS is supported, but the range
  of supported attributes is still limited
- clipping is limited to single paths, no mask support
- color gradients are not supported (limitation of reportlab)
- SVG ``ForeignObject`` elements are not supported.


Examples
--------

You can use ``svglib`` as a Python package e.g. like in the following
interactive Python session:

.. code:: python

    >>> from svglib.svglib import svg2rlg
    >>> from reportlab.graphics import renderPDF, renderPM
    >>>
    >>> drawing = svg2rlg("file.svg")
    >>> renderPDF.drawToFile(drawing, "file.pdf")
    >>> renderPM.drawToFile(drawing, "file.png", fmt="PNG")

Note that the second parameter of ``drawToFile`` can be any
`Python file object`_, like a ``BytesIO`` buffer if you don't want the result
to be written on disk for example.

In addition a script named ``svg2pdf`` can be used more easily from
the system command-line. Here is the output from ``svg2pdf -h``::

    usage: svg2pdf [-h] [-v] [-o PATH_PAT] [PATH [PATH ...]]

    svg2pdf v. x.x.x
    A converter from SVG to PDF (via ReportLab Graphics)

    positional arguments:
      PATH                  Input SVG file path with extension .svg or .svgz.

    optional arguments:
      -h, --help            show this help message and exit
      -v, --version         Print version number and exit.
      -o PATH_PAT, --output PATH_PAT
                            Set output path (incl. the placeholders: dirname,
                            basename,base, ext, now) in both, %(name)s and {name}
                            notations.

    examples:
      # convert path/file.svg to path/file.pdf
      svg2pdf path/file.svg

      # convert file1.svg to file1.pdf and file2.svgz to file2.pdf
      svg2pdf file1.svg file2.svgz

      # convert file.svg to out.pdf
      svg2pdf -o out.pdf file.svg

      # convert all SVG files in path/ to PDF files with names like:
      # path/file1.svg -> file1.pdf
      svg2pdf -o "%(base)s.pdf" path/file*.svg

      # like before but with timestamp in the PDF files:
      # path/file1.svg -> path/out-12-58-36-file1.pdf
      svg2pdf -o {{dirname}}/out-{{now.hour}}-{{now.minute}}-{{now.second}}-%(base)s.pdf path/file*.svg

    issues/pull requests:
        https://github.com/deeplook/svglib

    Copyleft by Dinu Gherman, 2008-2021 (LGPL 3):
        http://www.gnu.org/copyleft/gpl.html


Dependencies
------------

``Svglib`` depends mainly on the ``reportlab`` package, which provides
the abstractions for building complex ``Drawings`` which it can render
into different fileformats, including PDF, EPS, SVG and various bitmaps
ones. Other dependancies are ``lxml`` which is used in the context of SVG
CSS stylesheets.


Installation
------------

There are three ways to install ``svglib``.

1. Using ``pip``
++++++++++++++++

With the ``pip`` command on your system and a working internet
connection you can install the newest version of ``svglib`` with only
one command in a terminal::

    $ pip install svglib

You can also use ``pip`` to install the very latest version of the
repository from GitHub, but then you won't be able to conveniently
run the test suite::

    $ pip install git+https://github.com/deeplook/svglib


2. Using ``conda``
++++++++++++++++++

If you use Anaconda_ or Miniconda_ you are surely using its respective package
manager, Conda_, as well. In that case you should be able to install ``svglib``
using these simple commands::

    $ conda config --add channels conda-forge
    $ conda install svglib

``Svglib`` was kindly packaged for ``conda`` by nicoddemus_. See here more about
`svglib with conda`_.


3. Manual installation
+++++++++++++++++++++++

Alternatively, you can install a tarball like ``svglib-<version>.tar.gz``
after downloading it from the `svglib page on PyPI`_ or the
`svglib releases page on GitHub`_ and executing a sequence of commands
like shown here::

    $ tar xfz svglib-<version>.tar.gz
    $ cd svglib-<version>
    $ python setup.py install

This will install a Python package named ``svglib`` in the
``site-packages`` subfolder of your Python installation and a script
tool named ``svg2pdf`` in your ``bin`` directory, e.g. in
``/usr/local/bin``.


Testing
-------

The ``svglib`` tarball distribution contains a PyTest_ test suite
in the ``tests`` directory. There, in ``tests/README.rst``, you can
also read more about testing. You can run the testsuite e.g. like
shown in the following lines on the command-line::

    $ tar xfz svglib-<version>.tar.gz
    $ cd svglib-<version>
    $ PYTHONPATH=. py.test
    ======================== test session starts =========================
    platform darwin -- Python 3.7.3, pytest-5.0.1, py-1.8.0, pluggy-0.12.0
    rootdir: /Users/<USER>/repos/github/deeplook/svglib, inifile:
    plugins: cov-2.4.0
    collected 36 items

    tests/test_basic.py ............................
    tests/test_samples.py .s.s.s.s

    =============== 32 passed, 4 skipped in 49.18 seconds ================


Bug reports
-----------

Please report bugs on the `svglib issue tracker`_ on GitHub (pull
requests are also appreciated)!
If necessary, please include information about the operating system, as
well as the versions of ``svglib``, ReportLab and Python being used!


.. _SVG: http://www.w3.org/Graphics/SVG/
.. _W3C SVG test suite:
      http://www.w3.org/Graphics/SVG/WG/wiki/Test_Suite_Overview
.. _flags from Wikipedia:
      https://en.wikipedia.org/wiki/Gallery_of_sovereign_state_flags
.. _symbols from Wikipedia:
      https://en.wikipedia.org/wiki/List_of_symbols
.. _ReportLab: https://www.reportlab.com/opensource/
.. _RML: https://www.reportlab.com/software/rml-reference/
.. _svglib issue tracker: https://github.com/deeplook/svglib/issues
.. _PyTest: http://pytest.org
.. _svglib page on PyPI: https://pypi.org/project/svglib/
.. _svglib releases page on GitHub: https://github.com/deeplook/svglib/releases
.. _Python file object: https://docs.python.org/3/glossary.html#term-file-object
.. _Anaconda: https://www.anaconda.com/download/
.. _Miniconda: https://conda.io/miniconda.html
.. _Conda: https://conda.io
.. _svglib with conda: https://github.com/conda-forge/svglib-feedstock
.. _nicoddemus: https://github.com/nicoddemus
