#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证通道布局修复脚本
检查channel_display_widget.py中的修改是否正确应用

Author: Assistant
Date: 2025-08-14
"""

import os
import re

def verify_count_time_layout_fix():
    """验证计数和用时布局修复"""
    print("🔍 验证计数和用时布局修复...")
    
    file_path = "ui/components/channel_display_widget.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改点
    checks = [
        # 检查是否使用了权重分配
        (r'count_time_layout\.addLayout\(count_container,\s*3\)', "计数区域权重分配"),
        (r'count_time_layout\.addLayout\(time_container,\s*2\)', "用时区域权重分配"),
        
        # 检查是否增加了计数显示宽度
        (r'self\.test_count_label\.setMinimumWidth\(35\)', "计数标签最小宽度"),
        (r'self\.test_count_label\.setMaximumWidth\(50\)', "计数标签最大宽度"),
        
        # 检查是否使用了容器布局
        (r'count_container = QHBoxLayout\(\)', "计数容器布局"),
        (r'time_container = QHBoxLayout\(\)', "用时容器布局"),
    ]
    
    all_passed = True
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✅ {description}: 已修复")
        else:
            print(f"❌ {description}: 未找到修复")
            all_passed = False
    
    return all_passed

def verify_impedance_alignment_fix():
    """验证Rs和Rct对齐修复"""
    print("\n🔍 验证Rs和Rct对齐修复...")
    
    file_path = "ui/components/channel_display_widget.py"
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改点
    checks = [
        # 检查是否使用了内容容器
        (r'content_container = QHBoxLayout\(\)', "内容容器布局"),
        
        # 检查标题标签固定宽度
        (r'title_label\.setMinimumWidth\(65\)', "标题标签最小宽度"),
        (r'title_label\.setMaximumWidth\(65\)', "标题标签最大宽度"),
        
        # 检查数值标签宽度增加
        (r'value_label\.setMinimumWidth\(60\)', "数值标签最小宽度"),
        (r'value_label\.setMaximumWidth\(80\)', "数值标签最大宽度"),
        
        # 检查是否添加了边框样式
        (r'border: 1px solid #dee2e6', "数值框边框样式"),
        (r'background-color: #f8f9fa', "数值框背景色"),
        
        # 检查对齐方式
        (r'title_label\.setAlignment\(Qt\.AlignmentFlag\.AlignLeft\)', "标题左对齐"),
        (r'value_label\.setAlignment\(Qt\.AlignmentFlag\.AlignCenter\)', "数值居中对齐"),
    ]
    
    all_passed = True
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✅ {description}: 已修复")
        else:
            print(f"❌ {description}: 未找到修复")
            all_passed = False
    
    return all_passed

def check_function_signatures():
    """检查函数签名是否正确"""
    print("\n🔍 检查函数签名...")
    
    file_path = "ui/components/channel_display_widget.py"
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键函数是否存在
    functions = [
        (r'def _create_count_time_area\(self, layout\):', "_create_count_time_area函数"),
        (r'def _create_compact_impedance_area\(self, layout, title: str, object_name: str\):', "_create_compact_impedance_area函数"),
    ]
    
    all_passed = True
    for pattern, description in functions:
        if re.search(pattern, content):
            print(f"✅ {description}: 存在")
        else:
            print(f"❌ {description}: 不存在")
            all_passed = False
    
    return all_passed

def generate_summary_report():
    """生成修复总结报告"""
    print("\n" + "="*60)
    print("📋 通道布局修复总结报告")
    print("="*60)
    
    print("\n🎯 修复目标:")
    print("1. 计数和用时空间均分，给计数更多显示空间")
    print("2. Rs和Rct标题与数值框对齐")
    print("3. 改善整体视觉效果")
    
    print("\n🔧 具体修改:")
    print("1. 计数和用时布局:")
    print("   - 使用权重分配: 计数区域权重3，用时区域权重2")
    print("   - 增加计数显示宽度: 35-50px")
    print("   - 使用容器布局确保对齐")
    
    print("\n2. Rs和Rct布局:")
    print("   - 标题固定宽度65px确保对齐")
    print("   - 数值框宽度60-80px，居中对齐")
    print("   - 添加边框和背景色提升视觉效果")
    
    print("\n3. 样式优化:")
    print("   - 改善间距和边距")
    print("   - 统一字体大小和颜色")
    print("   - 添加视觉边框和背景")

def main():
    """主函数"""
    print("🚀 开始验证通道布局修复...")
    
    # 切换到正确的目录
    if os.path.exists("EIS_ZL/JCY5001AS_Clean_Source0"):
        os.chdir("EIS_ZL/JCY5001AS_Clean_Source0")
    
    # 执行验证
    result1 = verify_count_time_layout_fix()
    result2 = verify_impedance_alignment_fix()
    result3 = check_function_signatures()
    
    # 生成报告
    generate_summary_report()
    
    # 总结
    print("\n" + "="*60)
    if result1 and result2 and result3:
        print("🎉 所有修复验证通过！通道布局已成功优化。")
        print("\n📝 建议:")
        print("1. 重启主程序查看修复效果")
        print("2. 测试8个通道的显示是否一致")
        print("3. 验证在不同分辨率下的显示效果")
    else:
        print("⚠️ 部分修复可能未完全生效，请检查代码修改。")
    
    print("\n✨ 修复完成！")

if __name__ == "__main__":
    main()
